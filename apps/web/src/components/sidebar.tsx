import React from "react";

const Sidebar = () => {
  return (
    <div className="w-64 h-screen bg-gray-800 text-white absolute top-0">
      <header className="flex  items-center  px-8 py-6">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 space-y-4">
            {/* <div className="w-14 h-14 bg-gradient-to-r from-pink-600 to-pink-800 rounded-full flex items-center justify-center mx-auto  shadow-lg shadow-pink-500/25 group-hover:scale-110 transition-transform duration-300">
              <span className="text-gray-300 text-xl font-semibold">A</span>
            </div> */}
            <span className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
              GitGist
            </span>
          </div>
        </div>
      </header>
      <div className="p-8">
        <ul className="space-y-4">
          <li>
            <a href="#" className="text-white hover:text-gray-300">
              Folder Structure
            </a>
          </li>
          <li>
            <a href="#" className="text-white hover:text-gray-300">
              Tech Stack
            </a>
          </li>
          <li>
            <a href="#" className="text-white hover:text-gray-300">
              Settings
            </a>
          </li>
           <li>
            <a href="#" className="text-white hover:text-gray-300">
              Settings
            </a>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default Sidebar;
